/* تنسيق عام للصفحة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
}

/* تنسيق شريط التنقل */
.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

/* تنسيق البطاقات */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    margin-bottom: 20px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: bold;
}

/* تنسيق الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* تنسيق الحقول */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* تنسيق مكونات الخلطة */
.component-item {
    background-color: #fff;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.component-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.component-number {
    background-color: #0d6efd;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.remove-component {
    background-color: #dc3545;
    border: none;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-component:hover {
    background-color: #c82333;
    transform: scale(1.1);
}

/* تنسيق نتائج الحساب */
.result-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
    text-align: center;
}

.result-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.result-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* تنسيق الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table th {
    background-color: #343a40;
    color: white;
    font-weight: 600;
    border: none;
}

.table td {
    vertical-align: middle;
    border-color: #e9ecef;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* تنسيق التبويبات */
.tab-content {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تنسيق الرسائل */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* تنسيق النسب المئوية */
.percentage-display {
    font-weight: bold;
    color: #0d6efd;
}

.percentage-total {
    font-size: 1.1rem;
    font-weight: bold;
    padding: 10px;
    border-radius: 8px;
    text-align: center;
    margin-top: 10px;
}

.percentage-valid {
    background-color: #d1edff;
    color: #0c5460;
}

.percentage-invalid {
    background-color: #f8d7da;
    color: #721c24;
}

/* تنسيق الخلطات المحفوظة */
.saved-mix-card {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    background-color: white;
    transition: all 0.3s ease;
}

.saved-mix-card:hover {
    border-color: #0d6efd;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.mix-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #343a40;
    margin-bottom: 10px;
}

.mix-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.stat-item {
    text-align: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: bold;
    color: #0d6efd;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 2px;
}

/* تنسيق responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .component-item {
        padding: 10px;
    }
    
    .mix-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* تحسينات إضافية */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
