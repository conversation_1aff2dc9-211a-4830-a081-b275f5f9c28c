from app import app, db
from models import Ingredient

def init_sample_data():
    """إضافة بيانات أولية للمكونات العلفية الشائعة"""
    
    sample_ingredients = [
        {
            'name': 'ذرة صفراء',
            'protein': 8.5,
            'energy': 3.3,
            'fiber': 2.5,
            'price_per_kg': 0.450,
            'description': 'مصدر طاقة أساسي في الأعلاف'
        },
        {
            'name': 'شعير',
            'protein': 11.0,
            'energy': 2.8,
            'fiber': 5.5,
            'price_per_kg': 0.380,
            'description': 'حبوب علفية غنية بالطاقة'
        },
        {
            'name': 'كسبة فول الصويا',
            'protein': 44.0,
            'energy': 2.2,
            'fiber': 7.0,
            'price_per_kg': 0.650,
            'description': 'مصدر بروتين عالي الجودة'
        },
        {
            'name': 'نخالة القمح',
            'protein': 15.5,
            'energy': 2.0,
            'fiber': 11.0,
            'price_per_kg': 0.320,
            'description': 'مصدر ألياف وبروتين متوسط'
        },
        {
            'name': 'كسبة بذور عباد الشمس',
            'protein': 28.0,
            'energy': 1.8,
            'fiber': 25.0,
            'price_per_kg': 0.480,
            'description': 'مصدر بروتين وألياف'
        },
        {
            'name': 'دريس البرسيم',
            'protein': 18.0,
            'energy': 2.2,
            'fiber': 28.0,
            'price_per_kg': 0.280,
            'description': 'علف أخضر مجفف غني بالبروتين'
        },
        {
            'name': 'تبن القمح',
            'protein': 3.5,
            'energy': 1.5,
            'fiber': 40.0,
            'price_per_kg': 0.150,
            'description': 'مصدر ألياف خشنة'
        },
        {
            'name': 'كسبة القطن',
            'protein': 23.0,
            'energy': 1.9,
            'fiber': 22.0,
            'price_per_kg': 0.420,
            'description': 'مصدر بروتين متوسط الجودة'
        },
        {
            'name': 'مولاس (دبس)',
            'protein': 3.0,
            'energy': 2.5,
            'fiber': 0.0,
            'price_per_kg': 0.250,
            'description': 'مصدر طاقة سريعة ومحسن طعم'
        },
        {
            'name': 'كربونات الكالسيوم',
            'protein': 0.0,
            'energy': 0.0,
            'fiber': 0.0,
            'price_per_kg': 0.180,
            'description': 'مصدر كالسيوم للدواجن'
        },
        {
            'name': 'ملح طعام',
            'protein': 0.0,
            'energy': 0.0,
            'fiber': 0.0,
            'price_per_kg': 0.120,
            'description': 'مصدر صوديوم وكلوريد'
        },
        {
            'name': 'فيتامينات ومعادن',
            'protein': 0.0,
            'energy': 0.0,
            'fiber': 0.0,
            'price_per_kg': 2.500,
            'description': 'خليط فيتامينات ومعادن أساسية'
        }
    ]
    
    with app.app_context():
        # التحقق من وجود البيانات مسبقاً
        if Ingredient.query.count() > 0:
            print("البيانات موجودة مسبقاً")
            return
        
        # إضافة المكونات
        for ingredient_data in sample_ingredients:
            ingredient = Ingredient(**ingredient_data)
            db.session.add(ingredient)
        
        try:
            db.session.commit()
            print(f"تم إضافة {len(sample_ingredients)} مكون بنجاح")
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في إضافة البيانات: {e}")

if __name__ == '__main__':
    init_sample_data()
