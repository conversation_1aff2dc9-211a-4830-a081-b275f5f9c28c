from flask import Flask, render_template, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import os
from datetime import datetime

app = Flask(__name__)
CORS(app)

# إعداد قاعدة البيانات
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{os.path.join(basedir, "feed_mixer.db")}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = 'your-secret-key-here'

db = SQLAlchemy(app)

# تعريف النماذج هنا لتجنب مشاكل الاستيراد الدائري
class Ingredient(db.Model):
    """نموذج المكونات العلفية"""
    __tablename__ = 'ingredients'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    protein = db.Column(db.Float, nullable=False, default=0.0)
    energy = db.Column(db.Float, nullable=False, default=0.0)
    fiber = db.Column(db.Float, nullable=False, default=0.0)
    price_per_kg = db.Column(db.Float, nullable=False, default=0.0)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    mix_components = db.relationship('MixComponent', backref='ingredient', lazy=True, cascade='all, delete-orphan')

class FeedMix(db.Model):
    """نموذج الخلطات العلفية"""
    __tablename__ = 'feed_mixes'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    target_protein = db.Column(db.Float)
    target_energy = db.Column(db.Float)
    target_fiber = db.Column(db.Float)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    components = db.relationship('MixComponent', backref='feed_mix', lazy=True, cascade='all, delete-orphan')

class MixComponent(db.Model):
    """نموذج مكونات الخلطة"""
    __tablename__ = 'mix_components'

    id = db.Column(db.Integer, primary_key=True)
    feed_mix_id = db.Column(db.Integer, db.ForeignKey('feed_mixes.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    percentage = db.Column(db.Float, nullable=False)
    notes = db.Column(db.Text)

    __table_args__ = (db.UniqueConstraint('feed_mix_id', 'ingredient_id', name='unique_mix_ingredient'),)

# إنشاء الجداول
with app.app_context():
    db.create_all()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/ingredients', methods=['GET'])
def get_ingredients():
    ingredients = Ingredient.query.all()
    return jsonify([{
        'id': ing.id,
        'name': ing.name,
        'protein': ing.protein,
        'energy': ing.energy,
        'fiber': ing.fiber,
        'price_per_kg': ing.price_per_kg
    } for ing in ingredients])

@app.route('/api/ingredients', methods=['POST'])
def add_ingredient():
    data = request.json
    ingredient = Ingredient(
        name=data['name'],
        protein=data['protein'],
        energy=data['energy'],
        fiber=data['fiber'],
        price_per_kg=data['price_per_kg']
    )
    db.session.add(ingredient)
    db.session.commit()
    return jsonify({'message': 'تم إضافة المكون بنجاح', 'id': ingredient.id})

@app.route('/api/feed-mixes', methods=['GET'])
def get_feed_mixes():
    mixes = FeedMix.query.all()
    return jsonify([{
        'id': mix.id,
        'name': mix.name,
        'description': mix.description,
        'created_at': mix.created_at.isoformat(),
        'components': [{
            'ingredient_id': comp.ingredient_id,
            'ingredient_name': comp.ingredient.name,
            'percentage': comp.percentage
        } for comp in mix.components]
    } for mix in mixes])

@app.route('/api/ingredients/<int:ingredient_id>', methods=['PUT'])
def update_ingredient(ingredient_id):
    ingredient = Ingredient.query.get_or_404(ingredient_id)
    data = request.json

    try:
        ingredient.name = data['name']
        ingredient.protein = data['protein']
        ingredient.energy = data['energy']
        ingredient.fiber = data['fiber']
        ingredient.price_per_kg = data['price_per_kg']
        ingredient.description = data.get('description', '')

        db.session.commit()
        return jsonify({'message': 'تم تحديث المكون بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': 'خطأ في تحديث المكون'}), 400

@app.route('/api/ingredients/<int:ingredient_id>', methods=['DELETE'])
def delete_ingredient(ingredient_id):
    ingredient = Ingredient.query.get_or_404(ingredient_id)

    try:
        db.session.delete(ingredient)
        db.session.commit()
        return jsonify({'message': 'تم حذف المكون بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': 'خطأ في حذف المكون'}), 400

@app.route('/api/feed-mixes', methods=['POST'])
def create_feed_mix():
    data = request.json

    try:
        # إنشاء الخلطة
        feed_mix = FeedMix(
            name=data['name'],
            description=data.get('description', '')
        )
        db.session.add(feed_mix)
        db.session.flush()  # للحصول على ID الخلطة

        # إضافة المكونات
        for comp_data in data['components']:
            component = MixComponent(
                feed_mix_id=feed_mix.id,
                ingredient_id=comp_data['ingredient_id'],
                percentage=comp_data['percentage']
            )
            db.session.add(component)

        db.session.commit()
        return jsonify({'message': 'تم حفظ الخلطة بنجاح', 'id': feed_mix.id})
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': 'خطأ في حفظ الخلطة'}), 400

@app.route('/api/feed-mixes/<int:mix_id>', methods=['GET'])
def get_feed_mix(mix_id):
    mix = FeedMix.query.get_or_404(mix_id)
    return jsonify(mix.to_dict())

@app.route('/api/feed-mixes/<int:mix_id>', methods=['DELETE'])
def delete_feed_mix(mix_id):
    mix = FeedMix.query.get_or_404(mix_id)

    try:
        db.session.delete(mix)
        db.session.commit()
        return jsonify({'message': 'تم حذف الخلطة بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': 'خطأ في حذف الخلطة'}), 400

@app.route('/api/calculate-mix', methods=['POST'])
def calculate_mix():
    data = request.json
    components = data['components']  # [{'ingredient_id': 1, 'percentage': 50}, ...]
    weight_kg = data.get('weight_kg', 1)

    total_protein = 0
    total_energy = 0
    total_fiber = 0
    total_cost = 0

    for comp in components:
        ingredient = Ingredient.query.get(comp['ingredient_id'])
        if ingredient:
            percentage = comp['percentage'] / 100
            total_protein += ingredient.protein * percentage
            total_energy += ingredient.energy * percentage
            total_fiber += ingredient.fiber * percentage
            total_cost += ingredient.price_per_kg * percentage

    # حساب القيم للكيلو والطن
    result = {
        'per_kg': {
            'protein': round(total_protein, 2),
            'energy': round(total_energy, 2),
            'fiber': round(total_fiber, 2),
            'cost_jod': round(total_cost, 3)
        },
        'per_ton': {
            'protein': round(total_protein * 1000, 2),
            'energy': round(total_energy * 1000, 2),
            'fiber': round(total_fiber * 1000, 2),
            'cost_jod': round(total_cost * 1000, 2)
        },
        'for_weight': {
            'weight_kg': weight_kg,
            'protein': round(total_protein * weight_kg, 2),
            'energy': round(total_energy * weight_kg, 2),
            'fiber': round(total_fiber * weight_kg, 2),
            'cost_jod': round(total_cost * weight_kg, 3)
        }
    }

    return jsonify(result)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
