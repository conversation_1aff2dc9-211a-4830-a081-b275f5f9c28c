// متغيرات عامة
let ingredients = [];
let componentCounter = 0;
let currentMixResults = null;

// تحميل البيانات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    loadIngredients();
    loadSavedMixes();
    addComponent(); // إضافة مكون واحد افتراضياً
});

// إدارة التبويبات
function showTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.style.display = 'none';
    });
    
    // إزالة الفئة النشطة من جميع الروابط
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // إظهار التبويب المحدد
    document.getElementById(tabName + '-tab').style.display = 'block';
    
    // إضافة الفئة النشطة للرابط المحدد
    event.target.classList.add('active');
    
    // تحديث البيانات حسب التبويب
    if (tabName === 'ingredients') {
        loadIngredients();
    } else if (tabName === 'saved-mixes') {
        loadSavedMixes();
    }
}

// تحميل المكونات من الخادم
async function loadIngredients() {
    try {
        const response = await fetch('/api/ingredients');
        ingredients = await response.json();
        updateIngredientsTable();
        updateIngredientSelects();
    } catch (error) {
        console.error('خطأ في تحميل المكونات:', error);
        showAlert('خطأ في تحميل المكونات', 'danger');
    }
}

// تحديث جدول المكونات
function updateIngredientsTable() {
    const tbody = document.getElementById('ingredients-tbody');
    tbody.innerHTML = '';
    
    ingredients.forEach(ingredient => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${ingredient.name}</td>
            <td>${ingredient.protein}%</td>
            <td>${ingredient.energy}</td>
            <td>${ingredient.fiber}%</td>
            <td>${ingredient.price_per_kg.toFixed(3)} د.أ</td>
            <td>
                <button class="btn btn-sm btn-warning me-1" onclick="editIngredient(${ingredient.id})">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteIngredient(${ingredient.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// تحديث قوائم اختيار المكونات
function updateIngredientSelects() {
    const selects = document.querySelectorAll('.ingredient-select');
    selects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">اختر المكون</option>';
        
        ingredients.forEach(ingredient => {
            const option = document.createElement('option');
            option.value = ingredient.id;
            option.textContent = ingredient.name;
            if (ingredient.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    });
}

// إضافة مكون جديد للخلطة
function addComponent() {
    componentCounter++;
    const container = document.getElementById('components-container');
    
    const componentDiv = document.createElement('div');
    componentDiv.className = 'component-item fade-in';
    componentDiv.id = `component-${componentCounter}`;
    
    componentDiv.innerHTML = `
        <div class="component-header">
            <div class="component-number">${componentCounter}</div>
            <button class="remove-component" onclick="removeComponent(${componentCounter})" title="حذف المكون">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">المكون</label>
                <select class="form-select ingredient-select" onchange="updateComponentInfo(${componentCounter})">
                    <option value="">اختر المكون</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">النسبة المئوية (%)</label>
                <input type="number" class="form-control percentage-input" 
                       min="0" max="100" step="0.1" 
                       onchange="updatePercentageTotal()" 
                       placeholder="0.0">
            </div>
        </div>
        <div class="row mt-2" id="component-info-${componentCounter}" style="display: none;">
            <div class="col-md-3">
                <small class="text-muted">البروتين: <span class="component-protein">-</span>%</small>
            </div>
            <div class="col-md-3">
                <small class="text-muted">الطاقة: <span class="component-energy">-</span></small>
            </div>
            <div class="col-md-3">
                <small class="text-muted">الألياف: <span class="component-fiber">-</span>%</small>
            </div>
            <div class="col-md-3">
                <small class="text-muted">السعر: <span class="component-price">-</span> د.أ/كغ</small>
            </div>
        </div>
    `;
    
    container.appendChild(componentDiv);
    updateIngredientSelects();
    updatePercentageTotal();
}

// حذف مكون من الخلطة
function removeComponent(componentId) {
    const component = document.getElementById(`component-${componentId}`);
    if (component) {
        component.remove();
        updatePercentageTotal();
    }
}

// تحديث معلومات المكون
function updateComponentInfo(componentId) {
    const select = document.querySelector(`#component-${componentId} .ingredient-select`);
    const infoDiv = document.getElementById(`component-info-${componentId}`);
    
    if (select.value) {
        const ingredient = ingredients.find(ing => ing.id == select.value);
        if (ingredient) {
            infoDiv.style.display = 'block';
            infoDiv.querySelector('.component-protein').textContent = ingredient.protein;
            infoDiv.querySelector('.component-energy').textContent = ingredient.energy;
            infoDiv.querySelector('.component-fiber').textContent = ingredient.fiber;
            infoDiv.querySelector('.component-price').textContent = ingredient.price_per_kg.toFixed(3);
        }
    } else {
        infoDiv.style.display = 'none';
    }
}

// تحديث إجمالي النسب المئوية
function updatePercentageTotal() {
    const percentageInputs = document.querySelectorAll('.percentage-input');
    let total = 0;
    
    percentageInputs.forEach(input => {
        const value = parseFloat(input.value) || 0;
        total += value;
    });
    
    // إزالة عرض النسبة الإجمالية السابق
    const existingTotal = document.querySelector('.percentage-total');
    if (existingTotal) {
        existingTotal.remove();
    }
    
    // إضافة عرض النسبة الإجمالية الجديد
    const container = document.getElementById('components-container');
    const totalDiv = document.createElement('div');
    totalDiv.className = `percentage-total ${total === 100 ? 'percentage-valid' : 'percentage-invalid'}`;
    totalDiv.innerHTML = `إجمالي النسب: ${total.toFixed(1)}%`;
    container.appendChild(totalDiv);
}

// حساب الخلطة
async function calculateMix() {
    const components = [];
    const componentDivs = document.querySelectorAll('.component-item');
    
    componentDivs.forEach(div => {
        const select = div.querySelector('.ingredient-select');
        const percentageInput = div.querySelector('.percentage-input');
        
        if (select.value && percentageInput.value) {
            components.push({
                ingredient_id: parseInt(select.value),
                percentage: parseFloat(percentageInput.value)
            });
        }
    });
    
    if (components.length === 0) {
        showAlert('يرجى إضافة مكونات للخلطة', 'warning');
        return;
    }
    
    const totalPercentage = components.reduce((sum, comp) => sum + comp.percentage, 0);
    if (Math.abs(totalPercentage - 100) > 0.1) {
        showAlert('يجب أن يكون مجموع النسب المئوية = 100%', 'warning');
        return;
    }
    
    const targetWeight = parseFloat(document.getElementById('targetWeight').value) || 1;
    
    try {
        const response = await fetch('/api/calculate-mix', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                components: components,
                weight_kg: targetWeight
            })
        });
        
        const results = await response.json();
        currentMixResults = results;
        displayResults(results);
        showAlert('تم حساب الخلطة بنجاح', 'success');
    } catch (error) {
        console.error('خطأ في حساب الخلطة:', error);
        showAlert('خطأ في حساب الخلطة', 'danger');
    }
}

// عرض نتائج الحساب
function displayResults(results) {
    const container = document.getElementById('calculation-results');
    
    container.innerHTML = `
        <div class="result-item">
            <div class="result-value">${results.for_weight.protein} جم</div>
            <div class="result-label">البروتين (${results.for_weight.weight_kg} كغ)</div>
        </div>
        <div class="result-item">
            <div class="result-value">${results.for_weight.energy} م.ك</div>
            <div class="result-label">الطاقة (${results.for_weight.weight_kg} كغ)</div>
        </div>
        <div class="result-item">
            <div class="result-value">${results.for_weight.fiber} جم</div>
            <div class="result-label">الألياف (${results.for_weight.weight_kg} كغ)</div>
        </div>
        <div class="result-item">
            <div class="result-value">${results.for_weight.cost_jod} د.أ</div>
            <div class="result-label">التكلفة (${results.for_weight.weight_kg} كغ)</div>
        </div>
        
        <hr class="my-3">
        
        <h6 class="text-center mb-3">القيم لكل كيلوغرام:</h6>
        <div class="row text-center">
            <div class="col-6">
                <small>البروتين: ${results.per_kg.protein}%</small>
            </div>
            <div class="col-6">
                <small>الطاقة: ${results.per_kg.energy} م.ك</small>
            </div>
            <div class="col-6">
                <small>الألياف: ${results.per_kg.fiber}%</small>
            </div>
            <div class="col-6">
                <small>التكلفة: ${results.per_kg.cost_jod} د.أ</small>
            </div>
        </div>
        
        <hr class="my-3">
        
        <h6 class="text-center mb-3">القيم لكل طن:</h6>
        <div class="row text-center">
            <div class="col-6">
                <small>التكلفة: ${results.per_ton.cost_jod} د.أ</small>
            </div>
            <div class="col-6">
                <small>الطاقة: ${results.per_ton.energy} م.ك</small>
            </div>
        </div>
    `;
}

// مسح الخلطة
function clearMix() {
    if (confirm('هل أنت متأكد من مسح جميع المكونات؟')) {
        document.getElementById('mixName').value = '';
        document.getElementById('mixDescription').value = '';
        document.getElementById('targetWeight').value = '1';
        document.getElementById('components-container').innerHTML = '';
        document.getElementById('calculation-results').innerHTML = '<p class="text-muted text-center">قم بإضافة المكونات وحساب الخلطة لعرض النتائج</p>';
        componentCounter = 0;
        currentMixResults = null;
        addComponent();
    }
}

// عرض modal إضافة مكون
function showAddIngredientModal() {
    const modal = new bootstrap.Modal(document.getElementById('addIngredientModal'));
    document.getElementById('ingredient-form').reset();
    modal.show();
}

// حفظ مكون جديد
async function saveIngredient() {
    const form = document.getElementById('ingredient-form');
    const formData = new FormData(form);
    
    const ingredientData = {
        name: document.getElementById('ingredient-name').value,
        protein: parseFloat(document.getElementById('ingredient-protein').value),
        energy: parseFloat(document.getElementById('ingredient-energy').value),
        fiber: parseFloat(document.getElementById('ingredient-fiber').value),
        price_per_kg: parseFloat(document.getElementById('ingredient-price').value),
        description: document.getElementById('ingredient-description').value
    };
    
    try {
        const response = await fetch('/api/ingredients', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(ingredientData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showAlert(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addIngredientModal')).hide();
            loadIngredients();
        } else {
            showAlert(result.message || 'خطأ في حفظ المكون', 'danger');
        }
    } catch (error) {
        console.error('خطأ في حفظ المكون:', error);
        showAlert('خطأ في حفظ المكون', 'danger');
    }
}

// تحميل الخلطات المحفوظة
async function loadSavedMixes() {
    try {
        const response = await fetch('/api/feed-mixes');
        const mixes = await response.json();
        displaySavedMixes(mixes);
    } catch (error) {
        console.error('خطأ في تحميل الخلطات:', error);
        showAlert('خطأ في تحميل الخلطات المحفوظة', 'danger');
    }
}

// عرض الخلطات المحفوظة
function displaySavedMixes(mixes) {
    const container = document.getElementById('saved-mixes-container');
    
    if (mixes.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لا توجد خلطات محفوظة</p>';
        return;
    }
    
    container.innerHTML = mixes.map(mix => `
        <div class="saved-mix-card">
            <div class="mix-title">${mix.name}</div>
            <p class="text-muted">${mix.description || 'لا يوجد وصف'}</p>
            <div class="mix-stats">
                <div class="stat-item">
                    <div class="stat-value">${mix.actual_protein || 0}%</div>
                    <div class="stat-label">البروتين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${mix.actual_energy || 0}</div>
                    <div class="stat-label">الطاقة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${mix.actual_fiber || 0}%</div>
                    <div class="stat-label">الألياف</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${mix.cost_per_kg || 0} د.أ</div>
                    <div class="stat-label">التكلفة/كغ</div>
                </div>
            </div>
            <div class="mt-3">
                <button class="btn btn-primary btn-sm me-2" onclick="loadMix(${mix.id})">
                    <i class="fas fa-download me-1"></i>
                    تحميل الخلطة
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteMix(${mix.id})">
                    <i class="fas fa-trash me-1"></i>
                    حذف
                </button>
            </div>
        </div>
    `).join('');
}

// حفظ الخلطة
async function saveMix() {
    const mixName = document.getElementById('mixName').value.trim();
    if (!mixName) {
        showAlert('يرجى إدخال اسم الخلطة', 'warning');
        return;
    }

    if (!currentMixResults) {
        showAlert('يرجى حساب الخلطة أولاً', 'warning');
        return;
    }

    const components = [];
    const componentDivs = document.querySelectorAll('.component-item');

    componentDivs.forEach(div => {
        const select = div.querySelector('.ingredient-select');
        const percentageInput = div.querySelector('.percentage-input');

        if (select.value && percentageInput.value) {
            components.push({
                ingredient_id: parseInt(select.value),
                percentage: parseFloat(percentageInput.value)
            });
        }
    });

    const mixData = {
        name: mixName,
        description: document.getElementById('mixDescription').value,
        components: components
    };

    try {
        const response = await fetch('/api/feed-mixes', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(mixData)
        });

        const result = await response.json();

        if (response.ok) {
            showAlert('تم حفظ الخلطة بنجاح', 'success');
            loadSavedMixes();
        } else {
            showAlert(result.message || 'خطأ في حفظ الخلطة', 'danger');
        }
    } catch (error) {
        console.error('خطأ في حفظ الخلطة:', error);
        showAlert('خطأ في حفظ الخلطة', 'danger');
    }
}

// تحميل خلطة محفوظة
async function loadMix(mixId) {
    try {
        const response = await fetch(`/api/feed-mixes/${mixId}`);
        const mix = await response.json();

        // مسح الخلطة الحالية
        document.getElementById('components-container').innerHTML = '';
        componentCounter = 0;

        // تعبئة بيانات الخلطة
        document.getElementById('mixName').value = mix.name;
        document.getElementById('mixDescription').value = mix.description || '';

        // إضافة المكونات
        mix.components.forEach(component => {
            addComponent();
            const lastComponent = document.querySelector('.component-item:last-child');
            const select = lastComponent.querySelector('.ingredient-select');
            const percentageInput = lastComponent.querySelector('.percentage-input');

            select.value = component.ingredient_id;
            percentageInput.value = component.percentage;
            updateComponentInfo(componentCounter);
        });

        updatePercentageTotal();
        showTab('mixer');
        showAlert('تم تحميل الخلطة بنجاح', 'success');
    } catch (error) {
        console.error('خطأ في تحميل الخلطة:', error);
        showAlert('خطأ في تحميل الخلطة', 'danger');
    }
}

// حذف خلطة
async function deleteMix(mixId) {
    if (!confirm('هل أنت متأكد من حذف هذه الخلطة؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/feed-mixes/${mixId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showAlert('تم حذف الخلطة بنجاح', 'success');
            loadSavedMixes();
        } else {
            showAlert('خطأ في حذف الخلطة', 'danger');
        }
    } catch (error) {
        console.error('خطأ في حذف الخلطة:', error);
        showAlert('خطأ في حذف الخلطة', 'danger');
    }
}

// تعديل مكون
function editIngredient(ingredientId) {
    const ingredient = ingredients.find(ing => ing.id === ingredientId);
    if (!ingredient) return;

    // تعبئة النموذج ببيانات المكون
    document.getElementById('ingredient-name').value = ingredient.name;
    document.getElementById('ingredient-protein').value = ingredient.protein;
    document.getElementById('ingredient-energy').value = ingredient.energy;
    document.getElementById('ingredient-fiber').value = ingredient.fiber;
    document.getElementById('ingredient-price').value = ingredient.price_per_kg;
    document.getElementById('ingredient-description').value = ingredient.description || '';

    // تغيير عنوان النموذج
    document.querySelector('#addIngredientModal .modal-title').textContent = 'تعديل المكون';

    // تغيير وظيفة الزر
    const saveButton = document.querySelector('#addIngredientModal .btn-primary');
    saveButton.textContent = 'تحديث المكون';
    saveButton.onclick = () => updateIngredient(ingredientId);

    const modal = new bootstrap.Modal(document.getElementById('addIngredientModal'));
    modal.show();
}

// تحديث مكون
async function updateIngredient(ingredientId) {
    const ingredientData = {
        name: document.getElementById('ingredient-name').value,
        protein: parseFloat(document.getElementById('ingredient-protein').value),
        energy: parseFloat(document.getElementById('ingredient-energy').value),
        fiber: parseFloat(document.getElementById('ingredient-fiber').value),
        price_per_kg: parseFloat(document.getElementById('ingredient-price').value),
        description: document.getElementById('ingredient-description').value
    };

    try {
        const response = await fetch(`/api/ingredients/${ingredientId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(ingredientData)
        });

        const result = await response.json();

        if (response.ok) {
            showAlert('تم تحديث المكون بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addIngredientModal')).hide();
            loadIngredients();
            resetIngredientModal();
        } else {
            showAlert(result.message || 'خطأ في تحديث المكون', 'danger');
        }
    } catch (error) {
        console.error('خطأ في تحديث المكون:', error);
        showAlert('خطأ في تحديث المكون', 'danger');
    }
}

// حذف مكون
async function deleteIngredient(ingredientId) {
    if (!confirm('هل أنت متأكد من حذف هذا المكون؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/ingredients/${ingredientId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showAlert('تم حذف المكون بنجاح', 'success');
            loadIngredients();
        } else {
            showAlert('خطأ في حذف المكون', 'danger');
        }
    } catch (error) {
        console.error('خطأ في حذف المكون:', error);
        showAlert('خطأ في حذف المكون', 'danger');
    }
}

// إعادة تعيين نموذج المكون
function resetIngredientModal() {
    document.querySelector('#addIngredientModal .modal-title').textContent = 'إضافة مكون جديد';
    const saveButton = document.querySelector('#addIngredientModal .btn-primary');
    saveButton.textContent = 'حفظ المكون';
    saveButton.onclick = saveIngredient;
}

// عرض رسالة تنبيه
function showAlert(message, type) {
    // إزالة الرسائل السابقة
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);

    // إزالة الرسالة تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
