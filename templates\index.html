<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج تكوين الخلطات العلفية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-seedling me-2"></i>
                برنامج تكوين الخلطات العلفية
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showTab('mixer')">
                            <i class="fas fa-blender me-1"></i>
                            تكوين الخلطات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showTab('ingredients')">
                            <i class="fas fa-list me-1"></i>
                            إدارة المكونات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showTab('saved-mixes')">
                            <i class="fas fa-save me-1"></i>
                            الخلطات المحفوظة
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- تبويب تكوين الخلطات -->
        <div id="mixer-tab" class="tab-content">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-plus-circle me-2"></i>
                                تكوين خلطة جديدة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">اسم الخلطة</label>
                                    <input type="text" class="form-control" id="mixName" placeholder="أدخل اسم الخلطة">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">الوزن المطلوب (كغ)</label>
                                    <input type="number" class="form-control" id="targetWeight" value="1" min="0.1" step="0.1">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">وصف الخلطة</label>
                                <textarea class="form-control" id="mixDescription" rows="2" placeholder="وصف اختياري للخلطة"></textarea>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6>مكونات الخلطة</h6>
                                <button class="btn btn-success btn-sm" onclick="addComponent()">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة مكون
                                </button>
                            </div>

                            <div id="components-container">
                                <!-- سيتم إضافة المكونات هنا ديناميكياً -->
                            </div>

                            <div class="mt-3">
                                <button class="btn btn-primary me-2" onclick="calculateMix()">
                                    <i class="fas fa-calculator me-1"></i>
                                    حساب الخلطة
                                </button>
                                <button class="btn btn-success me-2" onclick="saveMix()">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ الخلطة
                                </button>
                                <button class="btn btn-secondary" onclick="clearMix()">
                                    <i class="fas fa-trash me-1"></i>
                                    مسح الكل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                نتائج الحساب
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="calculation-results">
                                <p class="text-muted text-center">قم بإضافة المكونات وحساب الخلطة لعرض النتائج</p>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                ملاحظات مهمة
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="small mb-0">
                                <li>يجب أن يكون مجموع النسب المئوية = 100%</li>
                                <li>الأسعار بالدينار الأردني</li>
                                <li>البروتين والألياف بالنسبة المئوية</li>
                                <li>الطاقة بالميجا كالوري/كغ</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويب إدارة المكونات -->
        <div id="ingredients-tab" class="tab-content" style="display: none;">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إدارة المكونات العلفية
                    </h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary mb-3" onclick="showAddIngredientModal()">
                        <i class="fas fa-plus me-1"></i>
                        إضافة مكون جديد
                    </button>
                    
                    <div class="table-responsive">
                        <table class="table table-striped" id="ingredients-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم المكون</th>
                                    <th>البروتين (%)</th>
                                    <th>الطاقة (ميجا كالوري/كغ)</th>
                                    <th>الألياف (%)</th>
                                    <th>السعر (د.أ/كغ)</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="ingredients-tbody">
                                <!-- سيتم تحميل المكونات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويب الخلطات المحفوظة -->
        <div id="saved-mixes-tab" class="tab-content" style="display: none;">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-archive me-2"></i>
                        الخلطات المحفوظة
                    </h5>
                </div>
                <div class="card-body">
                    <div id="saved-mixes-container">
                        <!-- سيتم تحميل الخلطات المحفوظة هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة مكون -->
    <div class="modal fade" id="addIngredientModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مكون جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="ingredient-form">
                        <div class="mb-3">
                            <label class="form-label">اسم المكون</label>
                            <input type="text" class="form-control" id="ingredient-name" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">البروتين (%)</label>
                                <input type="number" class="form-control" id="ingredient-protein" step="0.1" min="0" max="100" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الطاقة (ميجا كالوري/كغ)</label>
                                <input type="number" class="form-control" id="ingredient-energy" step="0.1" min="0" required>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">الألياف (%)</label>
                                <input type="number" class="form-control" id="ingredient-fiber" step="0.1" min="0" max="100" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">السعر (د.أ/كغ)</label>
                                <input type="number" class="form-control" id="ingredient-price" step="0.001" min="0" required>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="form-label">وصف المكون (اختياري)</label>
                            <textarea class="form-control" id="ingredient-description" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveIngredient()">حفظ المكون</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
