# برنامج تكوين الخلطات العلفية

## نظرة عامة
برنامج ويب متكامل لتكوين وحساب الخلطات العلفية باللغة العربية، مع دعم العملة الأردنية (الدينار الأردني). يوفر البرنامج واجهة سهلة الاستخدام لحساب القيم الغذائية والتكلفة للخلطات العلفية.

## المميزات الرئيسية

### 🌾 تكوين الخلطات
- إضافة مكونات متعددة للخلطة الواحدة
- تحديد النسب المئوية لكل مكون
- حساب تلقائي للقيم الغذائية والتكلفة
- عرض النتائج للكيلوغرام والطن والوزن المحدد

### 📊 الحسابات الغذائية
- **البروتين**: حساب نسبة البروتين الإجمالية
- **الطاقة**: حساب الطاقة بالميجا كالوري/كغ
- **الألياف**: حساب نسبة الألياف الخام
- **التكلفة**: حساب التكلفة بالدينار الأردني

### 🛠️ إدارة المكونات
- إضافة مكونات علفية جديدة
- تعديل أسعار وقيم المكونات الموجودة
- حذف المكونات غير المستخدمة
- قاعدة بيانات شاملة للمكونات الشائعة

### 💾 حفظ وإدارة الخلطات
- حفظ الخلطات المفضلة
- تحميل خلطات محفوظة سابقاً
- عرض تفاصيل كاملة للخلطات المحفوظة
- حذف الخلطات غير المرغوبة

## التقنيات المستخدمة

### Backend
- **Python 3.x**
- **Flask**: إطار عمل الويب
- **SQLAlchemy**: ORM لقاعدة البيانات
- **SQLite**: قاعدة البيانات المحلية

### Frontend
- **HTML5** مع دعم RTL للعربية
- **Bootstrap 5**: للتصميم المتجاوب
- **JavaScript**: للتفاعل الديناميكي
- **Font Awesome**: للأيقونات

## التثبيت والتشغيل

### المتطلبات
```bash
pip install -r requirements.txt
```

### إعداد قاعدة البيانات
```bash
python init_data.py
```

### تشغيل التطبيق
```bash
python app.py
```

ثم افتح المتصفح على: `http://localhost:5000`

## هيكل المشروع
```
علف/
├── app.py                 # التطبيق الرئيسي
├── init_data.py          # البيانات الأولية
├── requirements.txt      # المتطلبات
├── feed_mixer.db        # قاعدة البيانات
├── templates/
│   └── index.html       # الواجهة الرئيسية
└── static/
    ├── style.css        # ملف التنسيق
    └── script.js        # ملف JavaScript
```

## المكونات العلفية المتوفرة افتراضياً

| المكون | البروتين (%) | الطاقة (م.ك/كغ) | الألياف (%) | السعر (د.أ/كغ) |
|--------|-------------|----------------|-------------|---------------|
| ذرة صفراء | 8.5 | 3.3 | 2.5 | 0.450 |
| شعير | 11.0 | 2.8 | 5.5 | 0.380 |
| كسبة فول الصويا | 44.0 | 2.2 | 7.0 | 0.650 |
| نخالة القمح | 15.5 | 2.0 | 11.0 | 0.320 |
| كسبة بذور عباد الشمس | 28.0 | 1.8 | 25.0 | 0.480 |
| دريس البرسيم | 18.0 | 2.2 | 28.0 | 0.280 |
| تبن القمح | 3.5 | 1.5 | 40.0 | 0.150 |
| كسبة القطن | 23.0 | 1.9 | 22.0 | 0.420 |
| مولاس (دبس) | 3.0 | 2.5 | 0.0 | 0.250 |
| كربونات الكالسيوم | 0.0 | 0.0 | 0.0 | 0.180 |
| ملح طعام | 0.0 | 0.0 | 0.0 | 0.120 |
| فيتامينات ومعادن | 0.0 | 0.0 | 0.0 | 2.500 |

## واجهة برمجة التطبيقات (API)

### المكونات
- `GET /api/ingredients` - جلب جميع المكونات
- `POST /api/ingredients` - إضافة مكون جديد
- `PUT /api/ingredients/<id>` - تحديث مكون
- `DELETE /api/ingredients/<id>` - حذف مكون

### الخلطات
- `GET /api/feed-mixes` - جلب جميع الخلطات
- `POST /api/feed-mixes` - إنشاء خلطة جديدة
- `GET /api/feed-mixes/<id>` - جلب خلطة محددة
- `DELETE /api/feed-mixes/<id>` - حذف خلطة

### الحسابات
- `POST /api/calculate-mix` - حساب القيم الغذائية والتكلفة

## الاستخدام

1. **إضافة مكونات**: انتقل إلى تبويب "إدارة المكونات" لإضافة مكونات جديدة أو تعديل الموجودة
2. **تكوين خلطة**: في التبويب الرئيسي، أضف المكونات وحدد النسب المئوية
3. **حساب النتائج**: اضغط "حساب الخلطة" لعرض القيم الغذائية والتكلفة
4. **حفظ الخلطة**: احفظ الخلطات المفيدة للاستخدام المستقبلي

## الدعم والمساهمة
هذا البرنامج مفتوح المصدر ومرحب بالمساهمات لتحسينه وإضافة مميزات جديدة.

## الترخيص
هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.
